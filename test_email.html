<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email PICA</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #ffffff;
            background-color: #0f0f23;
            margin: 0;
            padding: 20px;
            font-size: 16px;
        }

        .email-container {
            width: 100%;
            background-color: #0f0f23;
        }

        .main-content {
            max-width: 600px;
            margin: 0 auto;
            background-color: #1e1b4b;
            border-radius: 20px;
            overflow: hidden;
            border: 2px solid #8b5cf6;
        }

        .header {
            background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
            padding: 40px;
            text-align: center;
            border-bottom: 2px solid #8b5cf6;
        }

        .logo-container {
            display: table;
            margin: 0 auto;
        }

        .logo-image {
            width: 80px;
            height: 80px;
            background-color: #0f0f23;
            border-radius: 16px;
            display: table-cell;
            text-align: center;
            vertical-align: middle;
            border: 2px solid #8b5cf6;
            margin: 0 auto 20px auto;
        }

        .logo-image svg {
            width: 50px;
            height: 50px;
        }

        .logo {
            text-align: center;
            margin-top: 20px;
        }

        .logo h1 {
            font-size: 2.8rem;
            font-weight: bold;
            margin: 0;
            color: #8b5cf6;
            line-height: 1;
        }

        .logo p {
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-weight: normal;
            margin: 8px 0 0 0;
            color: #ffffff;
            opacity: 0.8;
        }

        .content {
            padding: 40px;
            background-color: #1e1b4b;
        }

        .content h2 {
            font-size: 2.2rem;
            margin-bottom: 30px;
            color: #a855f7;
            text-align: center;
            font-weight: bold;
        }

        .content p {
            margin-bottom: 20px;
            line-height: 1.8;
            color: rgba(255, 255, 255, 0.95);
            font-size: 1.1rem;
        }

        .button {
            display: inline-block;
            background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
            color: white;
            text-decoration: none;
            padding: 18px 36px;
            border-radius: 12px;
            font-weight: bold;
            text-align: center;
            margin: 30px 0;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            border: 2px solid #8b5cf6;
        }

        .footer {
            padding: 30px 40px;
            text-align: center;
            border-top: 2px solid #8b5cf6;
            background-color: #1e1b4b;
            border-radius: 0 0 20px 20px;
        }

        .footer p {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
            font-weight: 500;
        }

        .footer a {
            color: #a855f7;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="main-content">
            <div class="header">
                <div class="logo-container">
                    <div class="logo-image">
                        <!-- PICA Logo SVG -->
                        <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            
                            <!-- Shield outline -->
                            <path d="M50 5 L15 20 L15 50 Q15 75 50 95 Q85 75 85 50 L85 20 Z" 
                                  fill="none" 
                                  stroke="url(#shieldGradient)" 
                                  stroke-width="2"/>
                            
                            <!-- PICA Text -->
                            <text x="50" y="55" 
                                  text-anchor="middle" 
                                  font-family="Arial, sans-serif" 
                                  font-size="18" 
                                  font-weight="bold" 
                                  fill="url(#textGradient)">PICA</text>
                        </svg>
                    </div>
                    <div class="logo">
                        <h1>PICA</h1>
                        <p>Plateforme de Cybersécurité Automatisée</p>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <h2>🔐 Password Reset Request</h2>
                <p>Hello,</p>
                <p>We received a request to reset the password for your PICA account. If you made this request, click the button below to create a new password.</p>
                
                <div style="text-align: center;">
                    <a href="#" class="button">🔑 Reset Your Password</a>
                </div>
                
                <p>Click the button below to securely reset your password:</p>
            </div>
            
            <div class="footer">
                <p>This email was sent by PICA Security Platform</p>
                <p>If you didn't request this action, please ignore this email or <a href="mailto:<EMAIL>">contact support</a></p>
                <p>&copy; 2025 PICA. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>
