{% extends "email_base_matrix.html" %}

{% block title %}PICA - System Access Reset{% endblock %}

{% block content %}
<div style="text-align:center; margin-bottom:30px;">
  <p style="color:#00ff41; font-size:14px; margin:0; font-family:'Courier New', monospace;">
    > SECURITY_ALERT: PASSWORD_RESET_REQUESTED
  </p>
</div>

<div class="info-box">
  <p style="color:#00ff41; margin:0 0 15px 0; font-family:'Courier New', monospace; font-size:14px;">
    > USER_AUTHENTICATION_REQUIRED<br>
    > SYSTEM_DETECTED: PASSWORD_RESET_REQUEST<br>
    > TIMESTAMP: {{ current_time or "2025-01-07 14:30:25" }}<br>
    > USER_ID: {{ user_email or "<EMAIL>" }}
  </p>
  
  <p style="color:#06b6d4; margin:15px 0; font-family:'Courier New', monospace; font-size:14px;">
    EXECUTE_COMMAND: Click the button below to reset your access credentials.
  </p>
  
  <p style="color:#ff0088; margin:0; font-family:'Courier New', monospace; font-size:12px;">
    WARNING: This link will expire in 10 minutes for security purposes.
  </p>
</div>

<!-- Matrix Button -->
<table cellpadding="0" cellspacing="0" border="0" style="margin:30px auto;">
  <tr>
    <td style="background: linear-gradient(45deg, #00ff41, #008f11); border:2px solid #00ff41; padding:0; box-shadow: 0 0 20px rgba(0,255,65,0.5);">
      <a href="{{ reset_link or '#' }}" target="_blank" style="display:block; padding:18px 36px; font-size:16px; color:#000000 !important; text-decoration:none !important; font-weight:bold; text-transform:uppercase; letter-spacing:2px; font-family:'Courier New', monospace; text-shadow: 0 0 5px rgba(0,0,0,0.8);">
        > RESET_PASSWORD
      </a>
    </td>
  </tr>
</table>

<div class="warning-box">
  <p style="color:#ff0088; margin:0; font-family:'Courier New', monospace; font-size:12px;">
    > SECURITY_NOTICE:<br>
    > IF_YOU_DID_NOT_REQUEST_THIS_RESET<br>
    > IGNORE_THIS_MESSAGE<br>
    > YOUR_ACCOUNT_REMAINS_SECURE
  </p>
</div>

<div class="divider"></div>

<p style="color:#06b6d4; text-align:center; margin:0; font-family:'Courier New', monospace; font-size:12px;">
  > END_OF_TRANSMISSION
</p>
{% endblock %}