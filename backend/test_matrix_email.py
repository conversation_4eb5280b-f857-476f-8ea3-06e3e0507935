#!/usr/bin/env python3
"""
Test script pour générer des exemples d'emails avec le thème Matrix
"""

from jinja2 import Environment, FileSystemLoader
import os

def generate_matrix_email_tests():
    """Génère des fichiers de test pour les emails Matrix"""
    
    # Configuration Jinja2
    template_dir = os.path.join(os.path.dirname(__file__), 'app', 'templates')
    env = Environment(loader=FileSystemLoader(template_dir))
    
    print("🔐 Testing PICA Matrix email templates...")
    print("=" * 50)
    
    # Test 1: Password Reset Matrix Style
    try:
        # Template Matrix pour reset password
        matrix_reset_content = """{% extends "email_base_matrix.html" %}

{% block title %}PICA - System Access Reset{% endblock %}

{% block content %}
<div style="text-align:center; margin-bottom:30px;">
  <p style="color:#00ff41; font-size:14px; margin:0; font-family:'Courier New', monospace;">
    > SECURITY_ALERT: PASSWORD_RESET_REQUESTED
  </p>
</div>

<div class="info-box">
  <p style="color:#00ff41; margin:0 0 15px 0; font-family:'Courier New', monospace; font-size:14px;">
    > USER_AUTHENTICATION_REQUIRED<br>
    > SYSTEM_DETECTED: PASSWORD_RESET_REQUEST<br>
    > TIMESTAMP: {{ current_time or "2025-01-07 14:30:25" }}<br>
    > USER_ID: {{ user_email or "<EMAIL>" }}
  </p>
  
  <p style="color:#06b6d4; margin:15px 0; font-family:'Courier New', monospace; font-size:14px;">
    EXECUTE_COMMAND: Click the button below to reset your access credentials.
  </p>
  
  <p style="color:#ff0088; margin:0; font-family:'Courier New', monospace; font-size:12px;">
    WARNING: This link will expire in 10 minutes for security purposes.
  </p>
</div>

<!-- Matrix Button -->
<table cellpadding="0" cellspacing="0" border="0" style="margin:30px auto;">
  <tr>
    <td style="background: linear-gradient(45deg, #00ff41, #008f11); border:2px solid #00ff41; padding:0; box-shadow: 0 0 20px rgba(0,255,65,0.5);">
      <a href="{{ reset_link or '#' }}" target="_blank" style="display:block; padding:18px 36px; font-size:16px; color:#000000 !important; text-decoration:none !important; font-weight:bold; text-transform:uppercase; letter-spacing:2px; font-family:'Courier New', monospace; text-shadow: 0 0 5px rgba(0,0,0,0.8);">
        > RESET_PASSWORD
      </a>
    </td>
  </tr>
</table>

<div class="warning-box">
  <p style="color:#ff0088; margin:0; font-family:'Courier New', monospace; font-size:12px;">
    > SECURITY_NOTICE:<br>
    > IF_YOU_DID_NOT_REQUEST_THIS_RESET<br>
    > IGNORE_THIS_MESSAGE<br>
    > YOUR_ACCOUNT_REMAINS_SECURE
  </p>
</div>

<div class="divider"></div>

<p style="color:#06b6d4; text-align:center; margin:0; font-family:'Courier New', monospace; font-size:12px;">
  > END_OF_TRANSMISSION
</p>
{% endblock %}"""

        # Sauvegarder le template
        with open('matrix_password_reset.html', 'w', encoding='utf-8') as f:
            f.write(matrix_reset_content)
        
        # Générer le test
        template = env.from_string(matrix_reset_content)
        html_content = template.render(
            reset_link="https://pica.com/reset-password?token=abc123",
            user_email="<EMAIL>",
            current_time="2025-01-07 14:30:25"
        )
        
        with open('test_matrix_password_reset.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("✅ Matrix password reset template generated: test_matrix_password_reset.html")
        
    except Exception as e:
        print(f"❌ Error generating matrix password reset: {e}")
    
    # Test 2: Email Confirmation Matrix Style
    try:
        matrix_confirm_content = """{% extends "email_base_matrix.html" %}

{% block title %}PICA - System Access Verification{% endblock %}

{% block content %}
<div style="text-align:center; margin-bottom:30px;">
  <p style="color:#00ff41; font-size:14px; margin:0; font-family:'Courier New', monospace;">
    > NEW_USER_REGISTRATION_DETECTED
  </p>
</div>

<div class="info-box">
  <p style="color:#00ff41; margin:0 0 15px 0; font-family:'Courier New', monospace; font-size:14px;">
    > SYSTEM_STATUS: EMAIL_VERIFICATION_REQUIRED<br>
    > NEW_ACCOUNT: {{ user_email or "<EMAIL>" }}<br>
    > REGISTRATION_TIME: {{ current_time or "2025-01-07 14:30:25" }}<br>
    > SECURITY_LEVEL: PENDING_VERIFICATION
  </p>
  
  <p style="color:#06b6d4; margin:15px 0; font-family:'Courier New', monospace; font-size:14px;">
    EXECUTE_COMMAND: Verify your email to activate full system access.
  </p>
</div>

<!-- Matrix Button -->
<table cellpadding="0" cellspacing="0" border="0" style="margin:30px auto;">
  <tr>
    <td style="background: linear-gradient(45deg, #00ff41, #008f11); border:2px solid #00ff41; padding:0; box-shadow: 0 0 20px rgba(0,255,65,0.5);">
      <a href="{{ confirmation_link or '#' }}" target="_blank" style="display:block; padding:18px 36px; font-size:16px; color:#000000 !important; text-decoration:none !important; font-weight:bold; text-transform:uppercase; letter-spacing:2px; font-family:'Courier New', monospace; text-shadow: 0 0 5px rgba(0,0,0,0.8);">
        > VERIFY_EMAIL
      </a>
    </td>
  </tr>
</table>

<div class="warning-box">
  <p style="color:#ff0088; margin:0; font-family:'Courier New', monospace; font-size:12px;">
    > SECURITY_PROTOCOL:<br>
    > LINK_EXPIRES_IN: 10_MINUTES<br>
    > SINGLE_USE_ONLY<br>
    > DO_NOT_SHARE_THIS_LINK
  </p>
</div>

<div class="divider"></div>

<p style="color:#06b6d4; text-align:center; margin:0; font-family:'Courier New', monospace; font-size:12px;">
  > WELCOME_TO_PICA_SECURITY_NETWORK<br>
  > END_OF_TRANSMISSION
</p>
{% endblock %}"""

        # Générer le test
        template = env.from_string(matrix_confirm_content)
        html_content = template.render(
            confirmation_link="https://pica.com/confirm-email?token=xyz789",
            user_email="<EMAIL>",
            current_time="2025-01-07 14:30:25"
        )
        
        with open('test_matrix_email_confirmation.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("✅ Matrix email confirmation template generated: test_matrix_email_confirmation.html")
        
    except Exception as e:
        print(f"❌ Error generating matrix email confirmation: {e}")
    
    print("=" * 50)
    print("✅ All Matrix email templates generated successfully!")
    print("🔐 Check the generated HTML files to preview the Matrix theme")
    print("💡 Open them in a web browser to see the full cyberpunk styling")

if __name__ == "__main__":
    generate_matrix_email_tests()
