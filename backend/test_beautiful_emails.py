#!/usr/bin/env python3
"""
Script de test pour les nouveaux templates d'email PICA améliorés
"""
import os
import sys
from flask import Flask, render_template

# Ajouter le répertoire parent au path pour importer l'app
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_app():
    """Créer une app Flask minimale pour tester les templates"""
    template_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'templates')
    app = Flask(__name__, template_folder=template_dir)
    return app

def test_password_reset():
    """Test du template de reset de mot de passe amélioré"""
    app = create_test_app()
    
    with app.app_context():
        html = render_template('password_reset.html',
            reset_link="http://localhost:5173/auth/reset-password?token=reset123",
            subject="PICA - Password Reset Request",
            current_year=2025,
            platform_url="http://localhost:5173"
        )
        
        with open('beautiful_password_reset.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print("✅ Beautiful password reset template generated: beautiful_password_reset.html")

def test_email_confirmation():
    """Test du template de confirmation d'email amélioré"""
    app = create_test_app()
    
    with app.app_context():
        html = render_template('email_confirmation.html', 
            first_name="John",
            confirm_url="http://localhost:5173/auth/email-confirmed?token=test123",
            subject="PICA - Confirm your email address",
            current_year=2025,
            platform_url="http://localhost:5173"
        )
        
        with open('beautiful_email_confirmation.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print("✅ Beautiful email confirmation template generated: beautiful_email_confirmation.html")

def main():
    """Fonction principale pour tester les templates améliorés"""
    print("🎨 Testing BEAUTIFUL PICA email templates...")
    print("=" * 60)
    
    try:
        test_password_reset()
        test_email_confirmation()
        
        print("=" * 60)
        print("✅ All BEAUTIFUL email templates generated successfully!")
        print("🎨 The new templates feature:")
        print("   • Bigger, bolder text with better readability")
        print("   • Enhanced purple/violet color scheme")
        print("   • Improved gradients and visual effects")
        print("   • Better spacing and modern typography")
        print("   • Flashy cybersecurity tech aesthetic")
        print("📁 Open the HTML files in a browser to see the improvements!")
        
    except Exception as e:
        print(f"❌ Error testing templates: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
