#!/usr/bin/env python3
"""
Script de test pour les templates d'email PICA
"""
import os
import sys
from flask import Flask, render_template

# Ajouter le répertoire parent au path pour importer l'app
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_app():
    """Créer une app Flask minimale pour tester les templates"""
    template_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'templates')
    app = Flask(__name__, template_folder=template_dir)
    return app

def test_email_confirmation():
    """Test du template de confirmation d'email"""
    app = create_test_app()
    
    with app.app_context():
        html = render_template('email_confirmation.html', 
            first_name="<PERSON>",
            confirm_url="http://localhost:5173/auth/email-confirmed?token=test123",
            subject="PICA - Confirm your email address",
            current_year=2025,
            platform_url="http://localhost:5173"
        )
        
        # Sauvegarder le HTML pour visualisation
        with open('test_email_confirmation.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print("✅ Email confirmation template generated: test_email_confirmation.html")

def test_password_reset():
    """Test du template de reset de mot de passe"""
    app = create_test_app()
    
    with app.app_context():
        html = render_template('password_reset.html',
            reset_link="http://localhost:5173/auth/reset-password?token=reset123",
            subject="PICA - Password Reset Request",
            current_year=2025,
            platform_url="http://localhost:5173"
        )
        
        with open('test_password_reset.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print("✅ Password reset template generated: test_password_reset.html")

def test_otp_verification():
    """Test du template de vérification OTP"""
    app = create_test_app()
    
    with app.app_context():
        html = render_template('otp_verification.html',
            otp="123456",
            subject="PICA - Login Verification Code",
            current_year=2025,
            platform_url="http://localhost:5173"
        )
        
        with open('test_otp_verification.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print("✅ OTP verification template generated: test_otp_verification.html")

def test_incident_notification():
    """Test du template de notification d'incident"""
    app = create_test_app()
    
    with app.app_context():
        html = render_template('incident_notification.html',
            notification_type="New Ticket Created",
            item_type="ticket",
            action="created",
            ticket_number="TKT-2024-001",
            short_description="Critical security vulnerability detected",
            category="Security",
            subcategory="Vulnerability",
            impact="High",
            urgency="High",
            priority="Critical",
            criticality_score=95,
            configuration_item="web-server-01",
            location="*************",
            created_by="<EMAIL>",
            status="New",
            created_date="2024-01-15 14:30:00",
            subject="PICA - New Ticket Created",
            current_year=2024,
            platform_url="http://localhost:5173/incidents"
        )
        
        with open('test_incident_notification.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print("✅ Incident notification template generated: test_incident_notification.html")

def main():
    """Fonction principale pour tester tous les templates"""
    print("🧪 Testing PICA email templates...")
    print("=" * 50)
    
    try:
        test_email_confirmation()
        test_password_reset()
        test_otp_verification()
        test_incident_notification()
        
        print("=" * 50)
        print("✅ All email templates generated successfully!")
        print("📁 Check the generated HTML files to preview the emails")
        print("💡 Open them in a web browser to see the full styling")
        
    except Exception as e:
        print(f"❌ Error testing templates: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
