{% extends "email_base.html" %}

{% block content %}
<h2>🚨 {{ notification_type }}</h2>

<p style="font-size: 1.15rem;">Hello,</p>

<p style="font-size: 1.15rem;">A new security <strong style="color: #a855f7;">{{ item_type }}</strong> has been <strong style="color: #06b6d4;">{{ action }}</strong> in the PICA platform and requires your attention.</p>

<div class="info-box">
    <p><strong style="color: #06b6d4; font-size: 1.4rem;">📋 {{ item_type|title }} Details:</strong></p>
    <table style="width: 100%; color: rgba(255, 255, 255, 0.95); margin-top: 20px; font-size: 1.1rem;">
        {% if ticket_number %}
        <tr>
            <td style="padding: 12px 0; font-weight: 700; color: #a855f7;">📝 Number:</td>
            <td style="padding: 12px 0; font-weight: 600;">{{ ticket_number }}</td>
        </tr>
        {% endif %}

        {% if short_description %}
        <tr>
            <td style="padding: 12px 0; font-weight: 700; color: #a855f7;">🖊 Description:</td>
            <td style="padding: 12px 0; font-weight: 600;">{{ short_description }}</td>
        </tr>
        {% endif %}
        
        {% if category %}
        <tr>
            <td style="padding: 8px 0; font-weight: 600;">🗂 Category:</td>
            <td style="padding: 8px 0;">{{ category }}</td>
        </tr>
        {% endif %}
        
        {% if subcategory %}
        <tr>
            <td style="padding: 8px 0; font-weight: 600;">🚩 Type:</td>
            <td style="padding: 8px 0;">{{ subcategory }}</td>
        </tr>
        {% endif %}
        
        {% if impact %}
        <tr>
            <td style="padding: 8px 0; font-weight: 600;">⚡ Impact:</td>
            <td style="padding: 8px 0;">
                <span style="background: {% if impact == 'High' %}#ef4444{% elif impact == 'Medium' %}#f59e0b{% else %}#10b981{% endif %}; padding: 4px 8px; border-radius: 6px; font-size: 0.85rem;">
                    {{ impact }}
                </span>
            </td>
        </tr>
        {% endif %}
        
        {% if urgency %}
        <tr>
            <td style="padding: 8px 0; font-weight: 600;">🔥 Urgency:</td>
            <td style="padding: 8px 0;">
                <span style="background: {% if urgency == 'High' %}#ef4444{% elif urgency == 'Medium' %}#f59e0b{% else %}#10b981{% endif %}; padding: 4px 8px; border-radius: 6px; font-size: 0.85rem;">
                    {{ urgency }}
                </span>
            </td>
        </tr>
        {% endif %}
        
        {% if priority %}
        <tr>
            <td style="padding: 8px 0; font-weight: 600;">🚨 Priority:</td>
            <td style="padding: 8px 0;">
                <span style="background: {% if priority == 'Critical' %}#dc2626{% elif priority == 'High' %}#ef4444{% elif priority == 'Medium' %}#f59e0b{% else %}#10b981{% endif %}; padding: 4px 8px; border-radius: 6px; font-size: 0.85rem;">
                    {{ priority }}
                </span>
            </td>
        </tr>
        {% endif %}
        
        {% if criticality_score %}
        <tr>
            <td style="padding: 8px 0; font-weight: 600;">🎯 Criticality Score:</td>
            <td style="padding: 8px 0;">{{ criticality_score }}/100</td>
        </tr>
        {% endif %}
        
        {% if configuration_item %}
        <tr>
            <td style="padding: 8px 0; font-weight: 600;">💻 Hostname:</td>
            <td style="padding: 8px 0;">{{ configuration_item }}</td>
        </tr>
        {% endif %}
        
        {% if location %}
        <tr>
            <td style="padding: 8px 0; font-weight: 600;">🌐 IP/Location:</td>
            <td style="padding: 8px 0;">{{ location }}</td>
        </tr>
        {% endif %}
        
        {% if created_by %}
        <tr>
            <td style="padding: 8px 0; font-weight: 600;">👤 Created by:</td>
            <td style="padding: 8px 0;">{{ created_by }}</td>
        </tr>
        {% endif %}
        
        {% if status %}
        <tr>
            <td style="padding: 8px 0; font-weight: 600;">🟩 Status:</td>
            <td style="padding: 8px 0;">
                <span style="background: {% if status == 'New' %}#06b6d4{% elif status == 'In Progress' %}#f59e0b{% elif status == 'Resolved' %}#10b981{% else %}#6b7280{% endif %}; padding: 4px 8px; border-radius: 6px; font-size: 0.85rem;">
                    {{ status }}
                </span>
            </td>
        </tr>
        {% endif %}
        
        {% if created_date %}
        <tr>
            <td style="padding: 8px 0; font-weight: 600;">🕒 Created on:</td>
            <td style="padding: 8px 0;">{{ created_date }}</td>
        </tr>
        {% endif %}
    </table>
</div>

<div style="text-align: center; margin: 40px 0;">
    <a href="{{ platform_url }}" class="button">
        🔗 View in PICA Platform
    </a>
</div>

{% if priority == 'Critical' or impact == 'High' %}
<div class="warning-box">
    <p><strong>⚠️ High Priority Alert:</strong></p>
    <p>This {{ item_type }} has been marked as high priority and requires immediate attention. Please review and take appropriate action as soon as possible.</p>
</div>
{% endif %}

<div class="divider"></div>

<p>This notification was automatically generated by the PICA security platform. Please log in to the platform for full details and to take action on this {{ item_type }}.</p>

<p>Best regards,<br>
<strong>PICA Security Team</strong></p>
{% endblock %}
