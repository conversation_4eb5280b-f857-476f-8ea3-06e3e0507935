<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ subject }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #ffffff;
            background-color: #0f0f23;
            margin: 0;
            padding: 20px;
            font-size: 16px;
        }

        .email-container {
            width: 100%;
            background-color: #0f0f23;
        }

        .main-content {
            max-width: 600px;
            margin: 0 auto;
            background-color: #1e1b4b;
            border-radius: 20px;
            overflow: hidden;
            border: 2px solid #8b5cf6;
        }

        .header {
            background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
            padding: 40px;
            text-align: center;
            border-bottom: 2px solid #8b5cf6;
        }

        .logo-container {
            display: table;
            margin: 0 auto;
        }

        .logo-image {
            width: 80px;
            height: 80px;
            background-color: #0f0f23;
            border-radius: 16px;
            display: table-cell;
            text-align: center;
            vertical-align: middle;
            border: 2px solid #8b5cf6;
            margin: 0 auto 20px auto;
        }

        .logo-image svg {
            width: 50px;
            height: 50px;
        }

        .logo {
            text-align: center;
            margin-top: 20px;
        }

        .logo h1 {
            font-size: 2.8rem;
            font-weight: bold;
            margin: 0;
            color: #8b5cf6;
            line-height: 1;
        }

        .logo p {
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-weight: normal;
            margin: 8px 0 0 0;
            color: #ffffff;
            opacity: 0.8;
        }
        
        .content {
            padding: 40px;
            position: relative;
            z-index: 2;
        }

        .content h2 {
            font-size: 2.2rem;
            margin-bottom: 30px;
            color: #a855f7;
            text-align: center;
            font-weight: bold;
        }

        .content p {
            margin-bottom: 20px;
            line-height: 1.8;
            color: rgba(255, 255, 255, 0.95);
            font-size: 1.1rem;
        }
        
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
            color: white;
            text-decoration: none;
            padding: 18px 36px;
            border-radius: 12px;
            font-weight: bold;
            text-align: center;
            margin: 30px 0;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            border: 2px solid #8b5cf6;
        }
        
        .info-box {
            background-color: rgba(139, 92, 246, 0.2);
            border: 2px solid #8b5cf6;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
        }

        .info-box p {
            font-size: 1.15rem;
            font-weight: 600;
        }

        .warning-box {
            background-color: rgba(245, 158, 11, 0.2);
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
        }

        .warning-box p {
            font-size: 1.15rem;
            font-weight: 600;
        }
        
        .footer {
            padding: 30px 40px;
            text-align: center;
            border-top: 2px solid #8b5cf6;
            background-color: #1e1b4b;
            border-radius: 0 0 20px 20px;
        }

        .footer p {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
            font-weight: 500;
        }

        .footer a {
            color: #a855f7;
            text-decoration: none;
            font-weight: 600;
        }

        .footer a:hover {
            text-decoration: underline;
            color: #06b6d4;
        }

        .divider {
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.6), rgba(6, 182, 212, 0.6), transparent);
            margin: 30px 0;
            border-radius: 1px;
        }
        
        @media (max-width: 600px) {
            body {
                font-size: 15px;
            }

            .header {
                padding: 40px 25px;
            }

            .logo-image {
                width: 100px;
                height: 100px;
            }

            .logo-image svg {
                width: 50px;
                height: 50px;
            }

            .logo h1 {
                font-size: 3rem;
            }

            .logo p {
                font-size: 1rem;
                letter-spacing: 3px;
            }

            .content {
                padding: 40px 30px;
            }

            .content h2 {
                font-size: 1.8rem;
            }

            .content p {
                font-size: 1rem;
            }

            .button {
                display: block;
                text-align: center;
                padding: 18px 35px;
                font-size: 1.1rem;
            }

            .info-box, .warning-box {
                padding: 25px 20px;
                margin: 25px 0;
            }

            .footer {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="main-content">
            <div class="header">
                <div class="logo-container">
                    <div class="logo-image">
                        <!-- PICA Logo SVG - Nouveau design cybersécurité -->
                        <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
                                </linearGradient>
                            </defs>

                            <!-- Shield outline with circuit pattern -->
                            <path d="M50 5 L15 20 L15 50 Q15 75 50 95 Q85 75 85 50 L85 20 Z"
                                  fill="none"
                                  stroke="url(#shieldGradient)"
                                  stroke-width="2"/>

                            <!-- Top circuit pattern -->
                            <path d="M25 25 L35 25 L40 30 L45 25 L55 25 L60 30 L65 25 L75 25"
                                  fill="none"
                                  stroke="url(#shieldGradient)"
                                  stroke-width="1.5"/>

                            <!-- PICA Text -->
                            <text x="50" y="55"
                                  text-anchor="middle"
                                  font-family="Arial, sans-serif"
                                  font-size="18"
                                  font-weight="bold"
                                  fill="url(#textGradient)">PICA</text>

                            <!-- Bottom circuit pattern -->
                            <path d="M25 75 L35 75 L40 70 L45 75 L55 75 L60 70 L65 75 L75 75"
                                  fill="none"
                                  stroke="url(#shieldGradient)"
                                  stroke-width="1.5"/>

                            <!-- Corner circuit nodes -->
                            <circle cx="25" cy="35" r="2" fill="url(#shieldGradient)"/>
                            <circle cx="75" cy="35" r="2" fill="url(#shieldGradient)"/>
                            <circle cx="25" cy="65" r="2" fill="url(#shieldGradient)"/>
                            <circle cx="75" cy="65" r="2" fill="url(#shieldGradient)"/>

                            <!-- Central security symbol -->
                            <circle cx="50" cy="35" r="3" fill="url(#shieldGradient)"/>
                            <path d="M45 35 L50 30 L55 35" fill="none" stroke="url(#textGradient)" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="logo">
                        <h1>PICA</h1>
                        <p>Plateforme de Cybersécurité Automatisée</p>
                    </div>
                </div>
            </div>

            <div class="content">
                {% block content %}{% endblock %}
            </div>

            <div class="footer">
                <p>This email was sent by PICA Security Platform</p>
                <p>If you didn't request this action, please ignore this email or <a href="mailto:<EMAIL>">contact support</a></p>
                <div class="divider"></div>
                <p>&copy; 2025 PICA. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>
