<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ subject }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.7;
            color: #ffffff;
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e293b 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            font-size: 16px;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e293b 100%);
            border-radius: 20px;
            overflow: hidden;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.5),
                0 0 40px rgba(139, 92, 246, 0.3),
                0 0 80px rgba(139, 92, 246, 0.2);
            border: 1px solid rgba(139, 92, 246, 0.3);
        }

        .email-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(139,92,246,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            opacity: 0.4;
            border-radius: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #06b6d4 100%);
            padding: 30px 40px;
            text-align: center;
            position: relative;
            z-index: 2;
            border-radius: 20px 20px 0 0;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="15" height="15" patternUnits="userSpaceOnUse"><path d="M 15 0 L 0 0 0 15" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="0.8"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>'),
                radial-gradient(circle at 20% 50%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(168, 85, 247, 0.2) 0%, transparent 50%);
            opacity: 0.6;
            animation: backgroundPulse 8s ease-in-out infinite;
        }

        @keyframes backgroundPulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 0.8; }
        }

        .logo-container {
            position: relative;
            z-index: 3;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            max-width: 350px;
            margin: 0 auto;
        }

        .logo-image {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(139, 92, 246, 0.6),
                0 0 40px rgba(139, 92, 246, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.2);
            flex-shrink: 0;
            animation: logoGlow 3s ease-in-out infinite alternate;
        }

        @keyframes logoGlow {
            0% {
                box-shadow:
                    0 8px 25px rgba(0, 0, 0, 0.3),
                    0 0 20px rgba(139, 92, 246, 0.6),
                    0 0 40px rgba(139, 92, 246, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
            100% {
                box-shadow:
                    0 8px 25px rgba(0, 0, 0, 0.3),
                    0 0 30px rgba(139, 92, 246, 0.8),
                    0 0 60px rgba(139, 92, 246, 0.6),
                    0 0 80px rgba(6, 182, 212, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
        }

        .logo-image svg {
            width: 40px;
            height: 40px;
            fill: #8b5cf6;
        }

        .logo {
            text-align: left;
        }

        .logo h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 0;
            text-shadow:
                0 3px 15px rgba(0, 0, 0, 0.4),
                0 0 20px rgba(255, 255, 255, 0.8),
                0 0 30px rgba(139, 92, 246, 0.6);
            background: linear-gradient(45deg, #ffffff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
            animation: textGlow 4s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            0% {
                text-shadow:
                    0 3px 15px rgba(0, 0, 0, 0.4),
                    0 0 20px rgba(255, 255, 255, 0.8),
                    0 0 30px rgba(139, 92, 246, 0.6);
            }
            100% {
                text-shadow:
                    0 3px 15px rgba(0, 0, 0, 0.4),
                    0 0 25px rgba(255, 255, 255, 1),
                    0 0 40px rgba(139, 92, 246, 0.8),
                    0 0 60px rgba(6, 182, 212, 0.6);
            }
        }

        .logo p {
            font-size: 0.9rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-weight: 500;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            margin: 5px 0 0 0;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .content {
            padding: 40px;
            position: relative;
            z-index: 2;
        }

        .content h2 {
            font-size: 2.2rem;
            margin-bottom: 30px;
            color: #a855f7;
            text-align: center;
            font-weight: 800;
            text-shadow:
                0 2px 15px rgba(168, 85, 247, 0.5),
                0 0 20px rgba(168, 85, 247, 0.8),
                0 0 40px rgba(168, 85, 247, 0.6),
                0 0 60px rgba(6, 182, 212, 0.4);
            animation: titleGlow 4s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            0% {
                text-shadow:
                    0 2px 15px rgba(168, 85, 247, 0.5),
                    0 0 20px rgba(168, 85, 247, 0.8),
                    0 0 40px rgba(168, 85, 247, 0.6);
            }
            100% {
                text-shadow:
                    0 2px 15px rgba(168, 85, 247, 0.7),
                    0 0 25px rgba(168, 85, 247, 1),
                    0 0 50px rgba(168, 85, 247, 0.8),
                    0 0 80px rgba(6, 182, 212, 0.6);
            }
        }

        .content p {
            margin-bottom: 20px;
            line-height: 1.8;
            color: rgba(255, 255, 255, 0.95);
            font-size: 1.1rem;
        }
        
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #06b6d4 100%);
            color: white;
            text-decoration: none;
            padding: 20px 40px;
            border-radius: 16px;
            font-weight: 700;
            text-align: center;
            margin: 30px 0;
            box-shadow:
                0 15px 35px rgba(139, 92, 246, 0.5),
                0 5px 15px rgba(6, 182, 212, 0.3),
                0 0 30px rgba(139, 92, 246, 0.6),
                0 0 60px rgba(139, 92, 246, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            font-size: 1.2rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            animation: buttonGlow 3s ease-in-out infinite alternate;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        @keyframes buttonGlow {
            0% {
                box-shadow:
                    0 15px 35px rgba(139, 92, 246, 0.5),
                    0 5px 15px rgba(6, 182, 212, 0.3),
                    0 0 30px rgba(139, 92, 246, 0.6),
                    0 0 60px rgba(139, 92, 246, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
            100% {
                box-shadow:
                    0 20px 45px rgba(139, 92, 246, 0.7),
                    0 8px 25px rgba(6, 182, 212, 0.5),
                    0 0 40px rgba(139, 92, 246, 0.8),
                    0 0 80px rgba(139, 92, 246, 0.6),
                    0 0 100px rgba(6, 182, 212, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
        }

        .button:hover {
            transform: translateY(-3px);
            box-shadow:
                0 25px 55px rgba(139, 92, 246, 0.8),
                0 10px 35px rgba(6, 182, 212, 0.6),
                0 0 50px rgba(139, 92, 246, 1),
                0 0 100px rgba(139, 92, 246, 0.8),
                0 0 120px rgba(6, 182, 212, 0.6);
            border-color: rgba(255, 255, 255, 0.4);
        }
        
        .info-box {
            background: rgba(139, 92, 246, 0.2);
            border: 2px solid rgba(139, 92, 246, 0.5);
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            backdrop-filter: blur(10px);
            box-shadow:
                0 10px 25px rgba(139, 92, 246, 0.3),
                0 0 20px rgba(139, 92, 246, 0.5),
                0 0 40px rgba(139, 92, 246, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            animation: infoBoxGlow 5s ease-in-out infinite alternate;
        }

        @keyframes infoBoxGlow {
            0% {
                box-shadow:
                    0 10px 25px rgba(139, 92, 246, 0.3),
                    0 0 20px rgba(139, 92, 246, 0.5),
                    0 0 40px rgba(139, 92, 246, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1);
            }
            100% {
                box-shadow:
                    0 15px 35px rgba(139, 92, 246, 0.5),
                    0 0 30px rgba(139, 92, 246, 0.7),
                    0 0 60px rgba(139, 92, 246, 0.5),
                    0 0 80px rgba(6, 182, 212, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
        }

        .info-box p {
            font-size: 1.15rem;
            font-weight: 600;
        }

        .warning-box {
            background: rgba(245, 158, 11, 0.2);
            border: 2px solid rgba(245, 158, 11, 0.5);
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            backdrop-filter: blur(10px);
            box-shadow:
                0 10px 25px rgba(245, 158, 11, 0.3),
                0 0 20px rgba(245, 158, 11, 0.5),
                0 0 40px rgba(245, 158, 11, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            animation: warningBoxGlow 5s ease-in-out infinite alternate;
        }

        @keyframes warningBoxGlow {
            0% {
                box-shadow:
                    0 10px 25px rgba(245, 158, 11, 0.3),
                    0 0 20px rgba(245, 158, 11, 0.5),
                    0 0 40px rgba(245, 158, 11, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1);
            }
            100% {
                box-shadow:
                    0 15px 35px rgba(245, 158, 11, 0.5),
                    0 0 30px rgba(245, 158, 11, 0.7),
                    0 0 60px rgba(245, 158, 11, 0.5),
                    0 0 80px rgba(239, 68, 68, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
        }

        .warning-box p {
            font-size: 1.15rem;
            font-weight: 600;
        }
        
        .footer {
            padding: 40px 50px;
            text-align: center;
            border-top: 2px solid rgba(139, 92, 246, 0.3);
            position: relative;
            z-index: 2;
            border-radius: 0 0 20px 20px;
        }

        .footer p {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
            font-weight: 500;
        }

        .footer a {
            color: #a855f7;
            text-decoration: none;
            font-weight: 600;
        }

        .footer a:hover {
            text-decoration: underline;
            color: #06b6d4;
        }

        .divider {
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.6), rgba(6, 182, 212, 0.6), transparent);
            margin: 30px 0;
            border-radius: 1px;
        }
        
        @media (max-width: 600px) {
            body {
                font-size: 15px;
            }

            .header {
                padding: 40px 25px;
            }

            .logo-image {
                width: 100px;
                height: 100px;
            }

            .logo-image svg {
                width: 50px;
                height: 50px;
            }

            .logo h1 {
                font-size: 3rem;
            }

            .logo p {
                font-size: 1rem;
                letter-spacing: 3px;
            }

            .content {
                padding: 40px 30px;
            }

            .content h2 {
                font-size: 1.8rem;
            }

            .content p {
                font-size: 1rem;
            }

            .button {
                display: block;
                text-align: center;
                padding: 18px 35px;
                font-size: 1.1rem;
            }

            .info-box, .warning-box {
                padding: 25px 20px;
                margin: 25px 0;
            }

            .footer {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo-container">
                <div class="logo-image">
                    <!-- PICA Logo SVG - Nouveau design cybersécurité -->
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <!-- Shield outline with circuit pattern -->
                        <path d="M50 5 L15 20 L15 50 Q15 75 50 95 Q85 75 85 50 L85 20 Z"
                              fill="none"
                              stroke="url(#shieldGradient)"
                              stroke-width="2"/>

                        <!-- Top circuit pattern -->
                        <path d="M25 25 L35 25 L40 30 L45 25 L55 25 L60 30 L65 25 L75 25"
                              fill="none"
                              stroke="url(#shieldGradient)"
                              stroke-width="1.5"/>

                        <!-- PICA Text -->
                        <text x="50" y="55"
                              text-anchor="middle"
                              font-family="Arial, sans-serif"
                              font-size="18"
                              font-weight="bold"
                              fill="url(#textGradient)">PICA</text>

                        <!-- Bottom circuit pattern -->
                        <path d="M25 75 L35 75 L40 70 L45 75 L55 75 L60 70 L65 75 L75 75"
                              fill="none"
                              stroke="url(#shieldGradient)"
                              stroke-width="1.5"/>

                        <!-- Corner circuit nodes -->
                        <circle cx="25" cy="35" r="2" fill="url(#shieldGradient)"/>
                        <circle cx="75" cy="35" r="2" fill="url(#shieldGradient)"/>
                        <circle cx="25" cy="65" r="2" fill="url(#shieldGradient)"/>
                        <circle cx="75" cy="65" r="2" fill="url(#shieldGradient)"/>

                        <!-- Central security symbol -->
                        <circle cx="50" cy="35" r="3" fill="url(#shieldGradient)"/>
                        <path d="M45 35 L50 30 L55 35" fill="none" stroke="url(#textGradient)" stroke-width="2"/>
                    </svg>
                </div>
                <div class="logo">
                    <h1>PICA</h1>
                    <p>Plateforme de Cybersécurité Automatisée</p>
                </div>
            </div>
        </div>
        
        <div class="content">
            {% block content %}{% endblock %}
        </div>
        
        <div class="footer">
            <p>This email was sent by PICA Security Platform</p>
            <p>If you didn't request this action, please ignore this email or <a href="mailto:<EMAIL>">contact support</a></p>
            <div class="divider"></div>
            <p>&copy; 2025 PICA. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
