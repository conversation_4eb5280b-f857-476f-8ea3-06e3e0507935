<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ subject }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.7;
            color: #ffffff;
            background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 50%, #06b6d4 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            font-size: 16px;
        }

        .email-container {
            width: 100%;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e293b 100%);
            position: relative;
            overflow: hidden;
        }

        .email-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(139,92,246,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            opacity: 0.6;
        }
        
        .header {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #06b6d4 100%);
            padding: 60px 40px;
            text-align: center;
            position: relative;
            z-index: 2;
            border-bottom: 4px solid rgba(139, 92, 246, 0.7);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="15" height="15" patternUnits="userSpaceOnUse"><path d="M 15 0 L 0 0 0 15" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="0.8"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.4;
        }

        .logo-container {
            position: relative;
            z-index: 3;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .logo-image {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 4px solid rgba(255, 255, 255, 0.2);
        }

        .logo-image svg {
            width: 60px;
            height: 60px;
            fill: #8b5cf6;
        }

        .logo h1 {
            font-size: 4rem;
            font-weight: 900;
            margin: 0;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
            background: linear-gradient(45deg, #ffffff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo p {
            font-size: 1.2rem;
            opacity: 0.95;
            text-transform: uppercase;
            letter-spacing: 4px;
            font-weight: 600;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            margin: 0;
        }
        
        .content {
            padding: 60px 50px;
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .content h2 {
            font-size: 2.2rem;
            margin-bottom: 30px;
            color: #a855f7;
            text-align: center;
            font-weight: 800;
            text-shadow: 0 2px 15px rgba(168, 85, 247, 0.5);
        }

        .content p {
            margin-bottom: 20px;
            line-height: 1.8;
            color: rgba(255, 255, 255, 0.95);
            font-size: 1.1rem;
        }
        
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #06b6d4 100%);
            color: white;
            text-decoration: none;
            padding: 20px 40px;
            border-radius: 16px;
            font-weight: 700;
            text-align: center;
            margin: 30px 0;
            box-shadow: 0 15px 35px rgba(139, 92, 246, 0.5), 0 5px 15px rgba(6, 182, 212, 0.3);
            transition: all 0.3s ease;
            font-size: 1.2rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .button:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 45px rgba(139, 92, 246, 0.6), 0 8px 25px rgba(6, 182, 212, 0.4);
            border-color: rgba(255, 255, 255, 0.4);
        }
        
        .info-box {
            background: rgba(139, 92, 246, 0.2);
            border: 2px solid rgba(139, 92, 246, 0.5);
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
        }

        .info-box p {
            font-size: 1.15rem;
            font-weight: 600;
        }

        .warning-box {
            background: rgba(245, 158, 11, 0.2);
            border: 2px solid rgba(245, 158, 11, 0.5);
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3);
        }

        .warning-box p {
            font-size: 1.15rem;
            font-weight: 600;
        }
        
        .footer {
            padding: 50px 50px;
            text-align: center;
            border-top: 4px solid rgba(139, 92, 246, 0.6);
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .footer p {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
            font-weight: 500;
        }

        .footer a {
            color: #a855f7;
            text-decoration: none;
            font-weight: 600;
        }

        .footer a:hover {
            text-decoration: underline;
            color: #06b6d4;
        }

        .divider {
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.6), rgba(6, 182, 212, 0.6), transparent);
            margin: 30px 0;
            border-radius: 1px;
        }
        
        @media (max-width: 600px) {
            body {
                font-size: 15px;
            }

            .header {
                padding: 40px 25px;
            }

            .logo-image {
                width: 100px;
                height: 100px;
            }

            .logo-image svg {
                width: 50px;
                height: 50px;
            }

            .logo h1 {
                font-size: 3rem;
            }

            .logo p {
                font-size: 1rem;
                letter-spacing: 3px;
            }

            .content {
                padding: 40px 30px;
            }

            .content h2 {
                font-size: 1.8rem;
            }

            .content p {
                font-size: 1rem;
            }

            .button {
                display: block;
                text-align: center;
                padding: 18px 35px;
                font-size: 1.1rem;
            }

            .info-box, .warning-box {
                padding: 25px 20px;
                margin: 25px 0;
            }

            .footer {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo-container">
                <div class="logo-image">
                    <!-- PICA Logo SVG -->
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <!-- Shield shape -->
                        <path d="M50 10 L20 25 L20 60 Q20 80 50 90 Q80 80 80 60 L80 25 Z" fill="#8b5cf6" stroke="#ffffff" stroke-width="2"/>
                        <!-- Lock icon -->
                        <rect x="40" y="45" width="20" height="15" rx="2" fill="#ffffff"/>
                        <path d="M42 45 L42 40 Q42 35 47 35 L53 35 Q58 35 58 40 L58 45" fill="none" stroke="#ffffff" stroke-width="2"/>
                        <!-- Circuit pattern -->
                        <circle cx="35" cy="35" r="2" fill="#06b6d4"/>
                        <circle cx="65" cy="35" r="2" fill="#06b6d4"/>
                        <circle cx="35" cy="65" r="2" fill="#06b6d4"/>
                        <circle cx="65" cy="65" r="2" fill="#06b6d4"/>
                        <line x1="35" y1="35" x2="40" y2="40" stroke="#06b6d4" stroke-width="1"/>
                        <line x1="65" y1="35" x2="60" y2="40" stroke="#06b6d4" stroke-width="1"/>
                        <line x1="35" y1="65" x2="40" y2="60" stroke="#06b6d4" stroke-width="1"/>
                        <line x1="65" y1="65" x2="60" y2="60" stroke="#06b6d4" stroke-width="1"/>
                    </svg>
                </div>
                <div class="logo">
                    <h1>PICA</h1>
                    <p>Plateforme de Cybersécurité Automatisée</p>
                </div>
            </div>
        </div>
        
        <div class="content">
            {% block content %}{% endblock %}
        </div>
        
        <div class="footer">
            <p>This email was sent by PICA Security Platform</p>
            <p>If you didn't request this action, please ignore this email or <a href="mailto:<EMAIL>">contact support</a></p>
            <div class="divider"></div>
            <p>&copy; 2025 PICA. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
